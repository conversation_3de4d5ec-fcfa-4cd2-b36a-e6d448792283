#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的状态更新测试
"""

def test_direct_status_update():
    """测试直接状态更新"""
    print("🧪 测试直接状态更新...")
    
    # 模拟状态更新
    statuses = {
        1: "OK",
        2: "NG",
        3: "OK", 
        4: "NG",
        5: "OK"
    }
    
    for process_id, status in statuses.items():
        print(f"进程{process_id}: {status}")
    
    print("✅ 状态更新测试完成")

if __name__ == "__main__":
    test_direct_status_update()
