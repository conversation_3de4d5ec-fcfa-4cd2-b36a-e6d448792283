#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接GUI状态更新器
"""

import os
import sys
import time
from datetime import datetime

def update_gui_status_directly():
    """直接更新GUI状态"""
    print("🎯 直接更新GUI状态...")
    
    # 这里应该直接调用GUI的状态更新函数
    # 而不是通过文件系统
    
    # 模拟状态更新
    statuses = {
        1: "OK",
        2: "NG", 
        3: "OK",
        4: "NG",
        5: "OK"
    }
    
    for process_id, status in statuses.items():
        print(f"✅ 进程{process_id}: {status}")
    
    print("✅ 直接状态更新完成")

if __name__ == "__main__":
    update_gui_status_directly()
