﻿using Cognex.VisionPro;
using Cognex.VisionPro.ToolBlock;
using OpenCvSharp;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Numerics;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.Runtime.CompilerServices; // 提供Unsafe类
using System.Threading.Tasks; // 并行处理
using System.Collections.Concurrent; // 对象池

namespace RunVppDLL
{
    public class Class1
    {
        CogToolBlock toolBlock = new CogToolBlock();

        public void LoadVpp(string vppPath)
        {
            try
            {
                if (!System.IO.File.Exists(vppPath))
                {
                    MessageBox.Show($"文件不存在: {vppPath}");
                    return;
                }

                toolBlock = CogSerializer.LoadObjectFromFile(vppPath) as CogToolBlock;

                if (toolBlock == null)
                {
                    MessageBox.Show("加载失败：文件内容无效或格式错误");
                }
                else
                {
                    MessageBox.Show("加载VPP成功!");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生错误: {ex.Message}");
            }
        }

        public void RunVpp()
        {
            try
            {
                toolBlock.Run();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生错误: {ex.Message}");
            }
        }
    }

    public class VisionProcessor
    {
        private CogToolBlock toolBlock;
        
        // 对象池：减少GC压力和内存分配
        private static readonly ConcurrentQueue<byte[]> ImageBufferPool = new ConcurrentQueue<byte[]>();
        private static readonly ConcurrentQueue<CogImage8Grey> CogImagePool = new ConcurrentQueue<CogImage8Grey>();
        
        // 缓存灰度调色板，避免重复创建
        private static ColorPalette _cachedGrayPalette;
        private static readonly object _paletteInitLock = new object();
        
        // 性能统计
        private int _processCount = 0;
        private double _totalProcessTime = 0;

        public VisionProcessor(string vppPath)
        {
            if (!System.IO.File.Exists(vppPath))
                throw new System.IO.FileNotFoundException("VPP文件不存在", vppPath);

            toolBlock = CogSerializer.LoadObjectFromFile(vppPath) as CogToolBlock;
            if (toolBlock == null)
                throw new InvalidCastException("VPP文件加载失败，不是有效的CogToolBlock");
                
            // 初始化灰度调色板缓存
            InitializeGrayPalette();
        }

        public int RunVppImg(CogImage8Grey image1)
        {
            if (image1 == null)
                throw new ArgumentNullException("输入图像为空");

            toolBlock.Inputs["Image"].Value = image1;
            toolBlock.Run();

            return toolBlock.RunStatus.Result == CogToolResultConstants.Accept ? 1 : 2;
        }

        public static CogImage8Grey ConvertMatToCogImage8Grey(Mat mat)
        {
            if (mat == null || mat.Empty())
                return null;

            if (mat.Channels() != 1)
            {
                using (Mat grayMat = new Mat())
                {
                    Cv2.CvtColor(mat, grayMat, ColorConversionCodes.BGR2GRAY);
                    mat = grayMat.Clone();
                }
            }

            CogImage8Grey cogImage = new CogImage8Grey();
            cogImage.Allocate(mat.Width, mat.Height);
            byte[] data = new byte[mat.Width * mat.Height];
            mat.GetArray(out data);

            for (int y = 0; y < mat.Height; y++)
                for (int x = 0; x < mat.Width; x++)
                    cogImage.SetPixel(x, y, data[y * mat.Width + x]);

            return cogImage;
        }

        public IntPtr ProcessBitmap(IntPtr bitmapPtr, out int resultStatus, out int resultWidth, out int resultHeight, out string OutputRes)
        {
            var startTime = DateTime.UtcNow;
            OutputRes = string.Empty;
            resultStatus = -1;
            resultWidth = 0;
            resultHeight = 0;

            if (bitmapPtr == IntPtr.Zero)
                throw new ArgumentNullException("输入Bitmap指针为空");

            try
            {
                using (Bitmap inputBitmap = (Bitmap)Image.FromHbitmap(bitmapPtr))
                {
                    Console.WriteLine($"输入图像格式: {inputBitmap.PixelFormat}");

                    // 优化1: 快速路径 - 如果已经是合法8位灰度图，直接使用
                    CogImage8Grey cogImage;
                    if (inputBitmap.PixelFormat == PixelFormat.Format8bppIndexed && IsGrayscalePalette(inputBitmap.Palette))
                    {
                        cogImage = FastConvertBitmapToCogImage(inputBitmap);
                        Console.WriteLine("快速路径: 直接使用8位灰度图");
                    }
                    else
                    {
                        // 优化2: 使用更高效的转换方法
                        using (var grayBitmap = OptimizedConvertTo8BitGray(inputBitmap))
                        {
                            cogImage = FastConvertBitmapToCogImage(grayBitmap);
                        }
                        Console.WriteLine("优化路径: 使用并行转换");
                    }
                    if (cogImage == null)
                        throw new InvalidOperationException("无法转换输入图像");

                    toolBlock.Inputs["Image"].Value = cogImage;
                    toolBlock.Run();

                    resultStatus = toolBlock.RunStatus.Result == CogToolResultConstants.Accept ? 1 : 2;
                    OutputRes = toolBlock.Outputs["OutputRes"].Value as string;

                    CogImage8Grey resultImage = toolBlock.Outputs["OutputImage"].Value as CogImage8Grey;
                    if (resultImage == null)
                        throw new InvalidOperationException("未找到结果图像");

                    resultWidth = resultImage.Width;
                    resultHeight = resultImage.Height;

                    // 优化3: 使用并行处理和对象池创建结果Bitmap
                    var resultBitmap = CreateResultBitmapOptimized(resultImage);
                    
                    // 清理资源
                    ReturnCogImageToPool(cogImage);

                    // 记录性能统计
                    var processingTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
                    UpdatePerformanceStats(processingTime);

                    return resultBitmap.GetHbitmap();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ProcessBitmap异常: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return IntPtr.Zero;
            }
        }

        private CogImage8Grey ConvertBitmapToCogImage(Bitmap bitmap)
        {
            if (bitmap == null)
                return null;

            // 确保bitmap是8位灰度图
            if (bitmap.PixelFormat != PixelFormat.Format8bppIndexed || !IsGrayscalePalette(bitmap.Palette))
            {
                Console.WriteLine($"ConvertBitmapToCogImage: 输入格式需要转换: {bitmap.PixelFormat}");
                bitmap = ConvertTo8BitGray(bitmap);
            }
            else
            {
                Console.WriteLine($"ConvertBitmapToCogImage: 输入已是合法8位灰度图");
            }

            BitmapData bitmapData = bitmap.LockBits(
                new Rectangle(0, 0, bitmap.Width, bitmap.Height),
                ImageLockMode.ReadOnly,
                PixelFormat.Format8bppIndexed
            );

            try
            {
                CogImage8Grey cogImage = new CogImage8Grey();
                cogImage.Allocate(bitmap.Width, bitmap.Height);

                byte[] pixelData = new byte[bitmapData.Stride * bitmap.Height];
                Marshal.Copy(bitmapData.Scan0, pixelData, 0, pixelData.Length);

                for (int y = 0; y < bitmap.Height; y++)
                {
                    for (int x = 0; x < bitmap.Width; x++)
                    {
                        cogImage.SetPixel(x, y, pixelData[y * bitmapData.Stride + x]);
                    }
                }

                return cogImage;
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }
        }

        // 增强版的ConvertTo8BitGray方法，完全避免Graphics对象
        //private static Bitmap ConvertTo8BitGray(Bitmap input)
        //{
        //    Console.WriteLine($"ConvertTo8BitGray: 开始转换输入格式: {input.PixelFormat}");

        //    // 先检查是否已是合法的8位灰度图
        //    if (input.PixelFormat == PixelFormat.Format8bppIndexed && IsGrayscalePalette(input.Palette))
        //    {
        //        Console.WriteLine($"ConvertTo8BitGray: 输入已是合法8位灰度图，直接返回副本");
        //        return new Bitmap(input);
        //    }

        //    int width = input.Width;
        //    int height = input.Height;

        //    // 创建临时32位ARGB图像（非索引格式，支持所有像素操作）
        //    using (Bitmap tempBitmap = new Bitmap(width, height, PixelFormat.Format32bppArgb))
        //    {
        //        // 手动复制像素数据，不使用Graphics对象
        //        BitmapData inputData = input.LockBits(
        //            new Rectangle(0, 0, width, height),
        //            ImageLockMode.ReadOnly,
        //            input.PixelFormat
        //        );

        //        BitmapData tempData = tempBitmap.LockBits(
        //            new Rectangle(0, 0, width, height),
        //            ImageLockMode.WriteOnly,
        //            PixelFormat.Format32bppArgb
        //        );

        //        try
        //        {
        //            // 手动复制像素数据
        //            unsafe
        //            {
        //                byte* inputPtr = (byte*)inputData.Scan0;
        //                byte* tempPtr = (byte*)tempData.Scan0;
        //                int inputStride = inputData.Stride;
        //                int tempStride = tempData.Stride;

        //                for (int y = 0; y < height; y++)
        //                {
        //                    for (int x = 0; x < width; x++)
        //                    {
        //                        // 根据输入图像格式获取像素值
        //                        byte r, g, b;

        //                        switch (input.PixelFormat)
        //                        {
        //                            case PixelFormat.Format24bppRgb:
        //                                b = inputPtr[y * inputStride + x * 3];
        //                                g = inputPtr[y * inputStride + x * 3 + 1];
        //                                r = inputPtr[y * inputStride + x * 3 + 2];
        //                                break;
        //                            case PixelFormat.Format32bppArgb:
        //                            case PixelFormat.Format32bppRgb:
        //                                b = inputPtr[y * inputStride + x * 4];
        //                                g = inputPtr[y * inputStride + x * 4 + 1];
        //                                r = inputPtr[y * inputStride + x * 4 + 2];
        //                                break;
        //                            case PixelFormat.Format8bppIndexed:
        //                                // 对于索引格式，直接获取索引值（假设为灰度）
        //                                byte index = inputPtr[y * inputStride + x];
        //                                r = g = b = index;
        //                                break;
        //                            default:
        //                                throw new NotSupportedException($"不支持的像素格式: {input.PixelFormat}");
        //                        }

        //                        // 设置临时图像的像素值（32位ARGB）
        //                        tempPtr[y * tempStride + x * 4] = b;       // B
        //                        tempPtr[y * tempStride + x * 4 + 1] = g;   // G
        //                        tempPtr[y * tempStride + x * 4 + 2] = r;   // R
        //                        tempPtr[y * tempStride + x * 4 + 3] = 255; // A（不透明）
        //                    }
        //                }
        //            }
        //        }
        //        finally
        //        {
        //            input.UnlockBits(inputData);
        //            tempBitmap.UnlockBits(tempData);
        //        }

        //        // 创建8位索引灰度图
        //        Bitmap grayBitmap = new Bitmap(width, height, PixelFormat.Format8bppIndexed);

        //        // 设置灰度调色板
        //        ColorPalette palette = grayBitmap.Palette;
        //        for (int i = 0; i < 256; i++)
        //        {
        //            palette.Entries[i] = Color.FromArgb(i, i, i);
        //        }
        //        grayBitmap.Palette = palette;

        //        // 再次锁定图像进行灰度转换
        //        tempData = tempBitmap.LockBits(
        //            new Rectangle(0, 0, width, height),
        //            ImageLockMode.ReadOnly,
        //            PixelFormat.Format32bppArgb
        //        );

        //        BitmapData grayData = grayBitmap.LockBits(
        //            new Rectangle(0, 0, width, height),
        //            ImageLockMode.WriteOnly,
        //            PixelFormat.Format8bppIndexed
        //        );

        //        try
        //        {
        //            // 计算灰度值并写入8位图像
        //            unsafe
        //            {
        //                byte* tempPtr = (byte*)tempData.Scan0;
        //                byte* grayPtr = (byte*)grayData.Scan0;
        //                int tempStride = tempData.Stride;
        //                int grayStride = grayData.Stride;

        //                for (int y = 0; y < height; y++)
        //                {
        //                    for (int x = 0; x < width; x++)
        //                    {
        //                        byte b = tempPtr[y * tempStride + x * 4];
        //                        byte g = tempPtr[y * tempStride + x * 4 + 1];
        //                        byte r = tempPtr[y * tempStride + x * 4 + 2];

        //                        // 计算灰度值（使用标准公式）
        //                        byte gray = (byte)(0.299 * r + 0.587 * g + 0.114 * b);

        //                        // 设置8位灰度图像的像素值
        //                        grayPtr[y * grayStride + x] = gray;
        //                    }
        //                }
        //            }
        //        }
        //        finally
        //        {
        //            tempBitmap.UnlockBits(tempData);
        //            grayBitmap.UnlockBits(grayData);
        //        }

        //        Console.WriteLine($"ConvertTo8BitGray: 成功转换为8位灰度图");
        //        return grayBitmap;
        //    }
        //}

        // 检查调色板是否为灰度

        // 静态缓存灰度调色板（全局复用，仅初始化一次）
        private static ColorPalette _grayPalette;
        // 初始化灰度调色板
        private static void InitGrayPalette()
        {
            if (_grayPalette == null)
            {
                using (var temp = new Bitmap(1, 1, PixelFormat.Format8bppIndexed))
                {
                    _grayPalette = temp.Palette;
                    for (int i = 0; i < 256; i++)
                    {
                        _grayPalette.Entries[i] = Color.FromArgb(i, i, i);
                    }
                }
            }
        }

        /// <summary>
        /// 将输入图像转换为8位灰度图（整合所有优化）
        /// </summary>
        public static Bitmap ConvertTo8BitGray(Bitmap input)
        {
            if (input == null)
                throw new ArgumentNullException(nameof(input));

            // 前置检查：若输入已是8位灰度图，直接返回副本
            if (input.PixelFormat == PixelFormat.Format8bppIndexed && IsGrayscalePalette(input.Palette))
            {
                return new Bitmap(input); // 直接复制，避免重复转换
            }

            InitGrayPalette(); // 确保调色板已初始化

            int width = input.Width;
            int height = input.Height;

            // 锁定输入图像内存
            var inputData = input.LockBits(
                new Rectangle(0, 0, width, height),
                ImageLockMode.ReadOnly,
                input.PixelFormat
            );

            try
            {
                // 创建目标8位灰度图
                var grayBitmap = new Bitmap(width, height, PixelFormat.Format8bppIndexed);
                grayBitmap.Palette = _grayPalette; // 复用缓存的灰度调色板

                var grayData = grayBitmap.LockBits(
                    new Rectangle(0, 0, width, height),
                    ImageLockMode.WriteOnly,
                    grayBitmap.PixelFormat
                );

                try
                {
                    unsafe
                    {
                        // 根据输入像素格式选择最优处理逻辑
                        switch (input.PixelFormat)
                        {
                            case PixelFormat.Format32bppArgb:
                            case PixelFormat.Format32bppRgb:
                            case PixelFormat.Format32bppPArgb:
                                Process32bpp(
                                    (byte*)inputData.Scan0,
                                    (byte*)grayData.Scan0,
                                    inputData.Stride,
                                    grayData.Stride,
                                    width,
                                    height
                                );
                                break;

                            case PixelFormat.Format24bppRgb:
                                Process24bpp(
                                    (byte*)inputData.Scan0,
                                    (byte*)grayData.Scan0,
                                    inputData.Stride,
                                    grayData.Stride,
                                    width,
                                    height
                                );
                                break;

                            case PixelFormat.Format8bppIndexed:
                                // 非灰度8位索引图：需通过原图像像素值转换
                                ProcessNonGray8bpp(
                                    input,
                                    (byte*)inputData.Scan0,
                                    (byte*)grayData.Scan0,
                                    inputData.Stride,
                                    grayData.Stride,
                                    width,
                                    height
                                );
                                break;

                            default:
                                // 不支持的格式：降级为32位中转处理（兼容模式）
                                ProcessUnknownFormat(
                                    input,
                                    (byte*)grayData.Scan0,
                                    grayData.Stride,
                                    width,
                                    height
                                );
                                break;
                        }
                    }
                }
                finally
                {
                    grayBitmap.UnlockBits(grayData); // 确保解锁目标图像
                }

                return grayBitmap;
            }
            finally
            {
                input.UnlockBits(inputData); // 确保解锁输入图像
            }
        }
        private static int CustomClamp(int value, int min, int max)
        {
            if (value < min) return min;
            if (value > max) return max;
            return value;
        }
        /// <summary>
        /// 处理32位像素格式（ARGB/RGB/PArgb）- 使用并行处理替代有问题的SIMD
        /// </summary>
        private static unsafe void Process32bpp(byte* inputPtr, byte* grayPtr, int inputStride, int grayStride, int width, int height)
        {
            // 使用并行处理替代有问题的SIMD代码
            Parallel.For(0, height, y =>
            {
                byte* inputRow = inputPtr + y * inputStride;
                byte* grayRow = grayPtr + y * grayStride;

                for (int x = 0; x < width; x++)
                {
                    byte* pixel = inputRow + x * 4;
                    byte b = pixel[0];
                    byte g = pixel[1];
                    byte r = pixel[2];
                    grayRow[x] = (byte)((299 * r + 587 * g + 114 * b) / 1000);
                }
            });
        }

        /// <summary>
        /// 处理24位像素格式（RGB）
        /// </summary>
        private static unsafe void Process24bpp(byte* inputPtr, byte* grayPtr, int inputStride, int grayStride, int width, int height)
        {
            var v299 = new Vector<int>(299);
            var v587 = new Vector<int>(587);
            var v114 = new Vector<int>(114);
            var v1000 = new Vector<int>(1000);
            int vectorLength = Vector<int>.Count;

            // 1. 创建临时数组（长度等于向量长度），用于存储像素值
            int[] rValues = new int[vectorLength];
            int[] gValues = new int[vectorLength];
            int[] bValues = new int[vectorLength];

            for (int y = 0; y < height; y++)
            {
                byte* inputRow = inputPtr + y * inputStride;
                byte* grayRow = grayPtr + y * grayStride;
                int x = 0;

                for (; x <= width - vectorLength; x += vectorLength)
                {
                    byte* basePtr = inputRow + x * 3;  // 当前批次像素的起始地址

                    // 2. 将像素值存入数组（替代直接给Vector元素赋值）
                    for (int i = 0; i < vectorLength; i++)
                    {
                        byte* pixelPtr = basePtr + i * 3;  // 第i个像素的地址
                        bValues[i] = pixelPtr[0];  // 存储B通道值到数组
                        gValues[i] = pixelPtr[1];  // 存储G通道值到数组
                        rValues[i] = pixelPtr[2];  // 存储R通道值到数组
                    }

                    // 3. 用数组初始化Vector（关键修正：避免直接修改Vector元素）
                    var r = new Vector<int>(rValues);
                    var g = new Vector<int>(gValues);
                    var b = new Vector<int>(bValues);

                    // 4. 计算灰度值（逻辑不变）
                    var grayVector = (v299 * r + v587 * g + v114 * b) / v1000;

                    // 5. 存储结果（注意指针访问方式）
                    for (int i = 0; i < vectorLength; i++)
                    {
                        *(grayRow + x + i) = (byte)CustomClamp(grayVector[i], 0, 255);
                    }
                }

                // 处理剩余像素（逻辑不变）
                for (; x < width; x++)
                {
                    byte* pixelPtr = inputRow + x * 3;
                    byte bVal = pixelPtr[0];
                    byte gVal = pixelPtr[1];
                    byte rVal = pixelPtr[2];
                    *(grayRow + x) = (byte)((299 * rVal + 587 * gVal + 114 * bVal) / 1000);
                }
            }
        }

        

        /// <summary>
        /// 处理非灰度的8位索引图（需通过原图像调色板转换）
        /// </summary>
        private static unsafe void ProcessNonGray8bpp(Bitmap input, byte* inputPtr, byte* grayPtr, int inputStride, int grayStride, int width, int height)
        {
            var palette = input.Palette; // 获取原图像调色板

            for (int y = 0; y < height; y++)
            {
                byte* inputRow = inputPtr + y * inputStride;
                byte* grayRow = grayPtr + y * grayStride;

                for (int x = 0; x < width; x++)
                {
                    // 从索引值获取原颜色，再转换为灰度
                    byte index = inputRow[x];
                    Color color = palette.Entries[index];
                    grayRow[x] = (byte)((299 * color.R + 587 * color.G + 114 * color.B) / 1000);
                }
            }
        }

        /// <summary>
        /// 处理未知格式（降级为32位中转处理）
        /// </summary>
        private static unsafe void ProcessUnknownFormat(Bitmap input, byte* grayPtr, int grayStride, int width, int height)
        {
            // 创建32位临时图像作为中转（仅在未知格式时使用）
            using (var temp = new Bitmap(width, height, PixelFormat.Format32bppArgb))
            {
                using (var g = Graphics.FromImage(temp))
                {
                    g.DrawImage(input, 0, 0, width, height); // 转为32位ARGB
                }

                var tempData = temp.LockBits(
                    new Rectangle(0, 0, width, height),
                    ImageLockMode.ReadOnly,
                    PixelFormat.Format32bppArgb
                );

                try
                {
                    unsafe
                    {
                        // 复用32位处理逻辑
                        Process32bpp(
                            (byte*)tempData.Scan0,
                            grayPtr,
                            tempData.Stride,
                            grayStride,
                            width,
                            height
                        );
                    }
                }
                finally
                {
                    temp.UnlockBits(tempData);
                }
            }
        }

        /// <summary>
        /// 检查调色板是否为灰度（R=G=B且Alpha=255）
        /// </summary>
        private static bool IsGrayscalePalette(ColorPalette palette)
        {
            foreach (var color in palette.Entries)
            {
                if (color.R != color.G || color.R != color.B || color.A != 255)
                {
                    return false;
                }
            }
            return true;
        }


        //private static bool IsGrayscalePalette(ColorPalette palette)
        //{
        //    for (int i = 0; i < palette.Entries.Length; i++)
        //    {
        //        Color c = palette.Entries[i];
        //        if (c.R != c.G || c.R != c.B || c.A != 255)
        //        {
        //            return false;
        //        }
        //    }
        //    return true;
        //}

        #region 新增的优化方法
        
        /// <summary>
        /// 优化的8位灰度转换 - 使用并行处理和缓存
        /// </summary>
        private static Bitmap OptimizedConvertTo8BitGray(Bitmap input)
        {
            if (input == null)
                throw new ArgumentNullException(nameof(input));

            // 快速检查：如果已经是8位灰度图，直接返回副本
            if (input.PixelFormat == PixelFormat.Format8bppIndexed && IsGrayscalePalette(input.Palette))
            {
                return new Bitmap(input);
            }

            int width = input.Width;
            int height = input.Height;

            var inputData = input.LockBits(
                new Rectangle(0, 0, width, height),
                ImageLockMode.ReadOnly,
                input.PixelFormat);

            try
            {
                var grayBitmap = new Bitmap(width, height, PixelFormat.Format8bppIndexed);
                grayBitmap.Palette = GetCachedGrayPalette();

                var grayData = grayBitmap.LockBits(
                    new Rectangle(0, 0, width, height),
                    ImageLockMode.WriteOnly,
                    grayBitmap.PixelFormat);

                try
                {
                    unsafe
                    {
                        // 根据格式选择最优处理路径
                        switch (input.PixelFormat)
                        {
                            case PixelFormat.Format32bppArgb:
                            case PixelFormat.Format32bppRgb:
                            case PixelFormat.Format32bppPArgb:
                                ParallelProcess32bpp(
                                    (byte*)inputData.Scan0,
                                    (byte*)grayData.Scan0,
                                    inputData.Stride,
                                    grayData.Stride,
                                    width,
                                    height);
                                break;

                            case PixelFormat.Format24bppRgb:
                                ParallelProcess24bpp(
                                    (byte*)inputData.Scan0,
                                    (byte*)grayData.Scan0,
                                    inputData.Stride,
                                    grayData.Stride,
                                    width,
                                    height);
                                break;

                            default:
                                // 降级为标准处理
                                StandardProcess(input, (byte*)grayData.Scan0, grayData.Stride, width, height);
                                break;
                        }
                    }
                }
                finally
                {
                    grayBitmap.UnlockBits(grayData);
                }

                return grayBitmap;
            }
            finally
            {
                input.UnlockBits(inputData);
            }
        }

        /// <summary>
        /// 并行处理32位图像 - 使用多线程
        /// </summary>
        private static unsafe void ParallelProcess32bpp(byte* inputPtr, byte* grayPtr, 
            int inputStride, int grayStride, int width, int height)
        {
            // 使用并行处理行
            Parallel.For(0, height, y =>
            {
                byte* inputRow = inputPtr + y * inputStride;
                byte* grayRow = grayPtr + y * grayStride;

                for (int x = 0; x < width; x++)
                {
                    byte* pixel = inputRow + x * 4;
                    byte b = pixel[0];
                    byte g = pixel[1];
                    byte r = pixel[2];
                    grayRow[x] = (byte)((299 * r + 587 * g + 114 * b) / 1000);
                }
            });
        }

        /// <summary>
        /// 并行处理24位图像
        /// </summary>
        private static unsafe void ParallelProcess24bpp(byte* inputPtr, byte* grayPtr,
            int inputStride, int grayStride, int width, int height)
        {
            Parallel.For(0, height, y =>
            {
                byte* inputRow = inputPtr + y * inputStride;
                byte* grayRow = grayPtr + y * grayStride;

                for (int x = 0; x < width; x++)
                {
                    byte* pixel = inputRow + x * 3;
                    byte b = pixel[0];
                    byte g = pixel[1];
                    byte r = pixel[2];
                    grayRow[x] = (byte)((299 * r + 587 * g + 114 * b) / 1000);
                }
            });
        }

        /// <summary>
        /// 快速转换Bitmap到CogImage - 使用对象池
        /// </summary>
        private static CogImage8Grey FastConvertBitmapToCogImage(Bitmap bitmap)
        {
            if (bitmap == null) return null;

            // 尝试从对象池获取
            CogImage8Grey cogImage = GetCogImageFromPool();
            cogImage.Allocate(bitmap.Width, bitmap.Height);

            var bitmapData = bitmap.LockBits(
                new Rectangle(0, 0, bitmap.Width, bitmap.Height),
                ImageLockMode.ReadOnly,
                PixelFormat.Format8bppIndexed);

            try
            {
                unsafe
                {
                    byte* srcPtr = (byte*)bitmapData.Scan0;
                    
                    // 使用并行处理提升性能
                    Parallel.For(0, bitmap.Height, y =>
                    {
                        byte* rowPtr = srcPtr + y * bitmapData.Stride;
                        for (int x = 0; x < bitmap.Width; x++)
                        {
                            cogImage.SetPixel(x, y, rowPtr[x]);
                        }
                    });
                }
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }

            return cogImage;
        }

        /// <summary>
        /// 优化的结果Bitmap创建 - 使用并行处理和对象池
        /// </summary>
        private static Bitmap CreateResultBitmapOptimized(CogImage8Grey resultImage)
        {
            int width = resultImage.Width;
            int height = resultImage.Height;

            var resultBitmap = new Bitmap(width, height, PixelFormat.Format8bppIndexed);
            resultBitmap.Palette = GetCachedGrayPalette();

            var bitmapData = resultBitmap.LockBits(
                new Rectangle(0, 0, width, height),
                ImageLockMode.WriteOnly,
                resultBitmap.PixelFormat);

            try
            {
                // 使用缓冲池获取临时数组
                byte[] imageData = GetBufferFromPool(width * height);

                // 并行读取像素数据
                Parallel.For(0, height, y =>
                {
                    for (int x = 0; x < width; x++)
                    {
                        imageData[y * width + x] = resultImage.GetPixel(x, y);
                    }
                });

                unsafe
                {
                    byte* destPtr = (byte*)bitmapData.Scan0;
                    int stride = bitmapData.Stride;

                    // 并行复制行数据
                    Parallel.For(0, height, y =>
                    {
                        Marshal.Copy(
                            imageData,
                            y * width,
                            new IntPtr(destPtr + y * stride),
                            width);
                    });
                }

                // 返回缓冲区到池
                ReturnBufferToPool(imageData);
            }
            finally
            {
                resultBitmap.UnlockBits(bitmapData);
            }

            return resultBitmap;
        }

        /// <summary>
        /// 标准处理方法（兼容性降级）
        /// </summary>
        private static unsafe void StandardProcess(Bitmap input, byte* grayPtr, int grayStride, int width, int height)
        {
            using (var temp = new Bitmap(width, height, PixelFormat.Format32bppArgb))
            {
                using (var g = Graphics.FromImage(temp))
                {
                    g.DrawImage(input, 0, 0, width, height);
                }

                var tempData = temp.LockBits(
                    new Rectangle(0, 0, width, height),
                    ImageLockMode.ReadOnly,
                    PixelFormat.Format32bppArgb);

                try
                {
                    ParallelProcess32bpp(
                        (byte*)tempData.Scan0,
                        grayPtr,
                        tempData.Stride,
                        grayStride,
                        width,
                        height);
                }
                finally
                {
                    temp.UnlockBits(tempData);
                }
            }
        }

        #endregion

        #region 对象池管理

        private static CogImage8Grey GetCogImageFromPool()
        {
            if (CogImagePool.TryDequeue(out CogImage8Grey cogImage))
            {
                return cogImage;
            }
            return new CogImage8Grey();
        }

        private static void ReturnCogImageToPool(CogImage8Grey cogImage)
        {
            if (cogImage != null && CogImagePool.Count < 10) // 限制池大小
            {
                CogImagePool.Enqueue(cogImage);
            }
        }

        private static byte[] GetBufferFromPool(int size)
        {
            if (ImageBufferPool.TryDequeue(out byte[] buffer) && buffer.Length >= size)
            {
                return buffer;
            }
            return new byte[size];
        }

        private static void ReturnBufferToPool(byte[] buffer)
        {
            if (buffer != null && buffer.Length <= 10 * 1024 * 1024 && // 最大10MB
                ImageBufferPool.Count < 5) // 限制池大小
            {
                ImageBufferPool.Enqueue(buffer);
            }
        }

        #endregion

        #region 辅助方法

        private static void InitializeGrayPalette()
        {
            if (_cachedGrayPalette == null)
            {
                lock (_paletteInitLock)
                {
                    if (_cachedGrayPalette == null)
                    {
                        using (var temp = new Bitmap(1, 1, PixelFormat.Format8bppIndexed))
                        {
                            _cachedGrayPalette = temp.Palette;
                            for (int i = 0; i < 256; i++)
                            {
                                _cachedGrayPalette.Entries[i] = Color.FromArgb(i, i, i);
                            }
                        }
                    }
                }
            }
        }

        private static ColorPalette GetCachedGrayPalette()
        {
            InitializeGrayPalette();
            return _cachedGrayPalette;
        }

        private void UpdatePerformanceStats(double processingTime)
        {
            _processCount++;
            _totalProcessTime += processingTime;
            
            if (_processCount % 100 == 0)
            {
                Console.WriteLine($"平均处理时间: {_totalProcessTime / _processCount:F2}ms ({_processCount} 次处理)");
            }
        }

        #endregion

        [DllImport("gdi32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool DeleteObject(IntPtr hObject);

        public void FreeBitmap(IntPtr hbitmap)
        {
            if (hbitmap != IntPtr.Zero)
            {
                DeleteObject(hbitmap);
            }
        }
    }
}