﻿using Cognex.VisionPro;
using Cognex.VisionPro.ToolBlock;
using OpenCvSharp;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace RunVppDLL
{
    public class Class1
    {
        CogToolBlock toolBlock = new CogToolBlock();

        public void LoadVpp(string vppPath)
        {
            try
            {
                if (!System.IO.File.Exists(vppPath))
                {
                    MessageBox.Show($"文件不存在: {vppPath}");
                    return;
                }

                toolBlock = CogSerializer.LoadObjectFromFile(vppPath) as CogToolBlock;

                if (toolBlock == null)
                {
                    MessageBox.Show("加载失败：文件内容无效或格式错误");
                }
                else
                {
                    MessageBox.Show("加载VPP成功!");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生错误: {ex.Message}");
            }
        }

        public void RunVpp()
        {
            try
            {
                toolBlock.Run();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生错误: {ex.Message}");
            }
        }
    }

    public class VisionProcessor
    {
        private CogToolBlock toolBlock;

        public VisionProcessor(string vppPath)
        {
            if (!System.IO.File.Exists(vppPath))
                throw new System.IO.FileNotFoundException("VPP文件不存在", vppPath);

            toolBlock = CogSerializer.LoadObjectFromFile(vppPath) as CogToolBlock;
            if (toolBlock == null)
                throw new InvalidCastException("VPP文件加载失败，不是有效的CogToolBlock");
        }

        public int RunVppImg(CogImage8Grey image1)
        {
            if (image1 == null)
                throw new ArgumentNullException("输入图像为空");

            toolBlock.Inputs["Image"].Value = image1;
            toolBlock.Run();

            return toolBlock.RunStatus.Result == CogToolResultConstants.Accept ? 1 : 2;
        }

        public static CogImage8Grey ConvertMatToCogImage8Grey(Mat mat)
        {
            if (mat == null || mat.Empty())
                return null;

            if (mat.Channels() != 1)
            {
                using (Mat grayMat = new Mat())
                {
                    Cv2.CvtColor(mat, grayMat, ColorConversionCodes.BGR2GRAY);
                    mat = grayMat.Clone();
                }
            }

            CogImage8Grey cogImage = new CogImage8Grey();
            cogImage.Allocate(mat.Width, mat.Height);
            byte[] data = new byte[mat.Width * mat.Height];
            mat.GetArray(out data);

            for (int y = 0; y < mat.Height; y++)
                for (int x = 0; x < mat.Width; x++)
                    cogImage.SetPixel(x, y, data[y * mat.Width + x]);

            return cogImage;
        }

        public IntPtr ProcessBitmap(IntPtr bitmapPtr, out int resultStatus, out int resultWidth, out int resultHeight, out string OutputRes)
        {
            OutputRes = string.Empty; // 初始化输出字符串
            if (bitmapPtr == IntPtr.Zero)
                throw new ArgumentNullException("输入Bitmap指针为空");

            try
            {
                using (Bitmap inputBitmap = (Bitmap)Image.FromHbitmap(bitmapPtr))
                {
                    Console.WriteLine($"输入图像格式: {inputBitmap.PixelFormat}"); // 调试输出

                    // 使用改进的转换方法，确保生成合法的8位灰度图
                    Bitmap grayBitmap = ConvertTo8BitGray(inputBitmap);

                    Console.WriteLine($"转换后图像格式: {grayBitmap.PixelFormat}"); // 调试输出

                    CogImage8Grey cogImage = ConvertBitmapToCogImage(grayBitmap);
                    if (cogImage == null)
                        throw new InvalidOperationException("无法转换输入图像");

                    toolBlock.Inputs["Image"].Value = cogImage;
                    toolBlock.Run();

                    resultStatus = toolBlock.RunStatus.Result == CogToolResultConstants.Accept ? 1 : 2;
                    OutputRes = toolBlock.Outputs["OutputRes"].Value as string;

                    CogImage8Grey resultImage = toolBlock.Outputs["OutputImage"].Value as CogImage8Grey;
                    if (resultImage == null)
                        throw new InvalidOperationException("未找到结果图像");

                    resultWidth = resultImage.Width;
                    resultHeight = resultImage.Height;

                    //Bitmap resultBitmap = new Bitmap(resultWidth, resultHeight, PixelFormat.Format8bppIndexed);
                    //BitmapData bitmapData = resultBitmap.LockBits(
                    //    new Rectangle(0, 0, resultWidth, resultHeight),
                    //    ImageLockMode.WriteOnly,
                    //    resultBitmap.PixelFormat
                    //);
                    // 创建结果Bitmap并复制数据
                    Bitmap resultBitmap = new Bitmap(resultWidth, resultHeight, PixelFormat.Format8bppIndexed);
                    BitmapData bitmapData = resultBitmap.LockBits(
                        new Rectangle(0, 0, resultWidth, resultHeight),
                        ImageLockMode.WriteOnly,
                        resultBitmap.PixelFormat
                    );
                    if (resultBitmap.Palette.Entries.Length > 0)
                    {
                        ColorPalette palette = resultBitmap.Palette;
                        for (int i = 0; i < 256; i++)
                        {
                            palette.Entries[i] = Color.FromArgb(i, i, i);
                        }
                        resultBitmap.Palette = palette;
                    }

                    byte[] imageData = new byte[resultWidth * resultHeight];
                    for (int y = 0; y < resultHeight; y++)
                    {
                        for (int x = 0; x < resultWidth; x++)
                        {
                            imageData[y * resultWidth + x] = resultImage.GetPixel(x, y);
                        }
                    }

                    // 按行复制数据到结果图像（关键修正）
                    unsafe
                    {
                        byte* destPtr = (byte*)bitmapData.Scan0;
                        int stride = bitmapData.Stride;

                        for (int y = 0; y < resultHeight; y++)
                        {
                            // 每行只复制有效宽度的像素（忽略Stride中的填充字节）
                            // 源数据起始索引：y * resultWidth（当前行的第一个像素）
                            // 目标行起始地址：destPtr + y * stride（当前行的内存起始位置）
                            // 复制长度：resultWidth（只复制有效像素）
                            Marshal.Copy(
                                imageData,
                                y * resultWidth,
                                new IntPtr(destPtr + y * stride),
                                resultWidth
                            );
                        }
                    }

                    // 解锁内存并返回指针
                    resultBitmap.UnlockBits(bitmapData);
                    IntPtr resultPtr = resultBitmap.GetHbitmap();
                    return resultPtr;

                    MessageBox.Show("输出图像完成");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"异常类型: {ex.GetType()}"); // 输出详细异常类型
                Console.WriteLine($"异常堆栈: {ex.StackTrace}"); // 输出完整堆栈
                resultStatus = -1;
                resultWidth = 0;
                resultHeight = 0;
                return IntPtr.Zero;
            }
        }

        private CogImage8Grey ConvertBitmapToCogImage(Bitmap bitmap)
        {
            if (bitmap == null)
                return null;

            // 确保bitmap是8位灰度图
            if (bitmap.PixelFormat != PixelFormat.Format8bppIndexed || !IsGrayscalePalette(bitmap.Palette))
            {
                Console.WriteLine($"ConvertBitmapToCogImage: 输入格式需要转换: {bitmap.PixelFormat}");
                bitmap = ConvertTo8BitGray(bitmap);
            }
            else
            {
                Console.WriteLine($"ConvertBitmapToCogImage: 输入已是合法8位灰度图");
            }

            BitmapData bitmapData = bitmap.LockBits(
                new Rectangle(0, 0, bitmap.Width, bitmap.Height),
                ImageLockMode.ReadOnly,
                PixelFormat.Format8bppIndexed
            );

            try
            {
                CogImage8Grey cogImage = new CogImage8Grey();
                cogImage.Allocate(bitmap.Width, bitmap.Height);

                byte[] pixelData = new byte[bitmapData.Stride * bitmap.Height];
                Marshal.Copy(bitmapData.Scan0, pixelData, 0, pixelData.Length);

                for (int y = 0; y < bitmap.Height; y++)
                {
                    for (int x = 0; x < bitmap.Width; x++)
                    {
                        cogImage.SetPixel(x, y, pixelData[y * bitmapData.Stride + x]);
                    }
                }

                return cogImage;
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }
        }

        // 增强版的ConvertTo8BitGray方法，完全避免Graphics对象
        //private static Bitmap ConvertTo8BitGray(Bitmap input)
        //{
        //    Console.WriteLine($"ConvertTo8BitGray: 开始转换输入格式: {input.PixelFormat}");

        //    // 先检查是否已是合法的8位灰度图
        //    if (input.PixelFormat == PixelFormat.Format8bppIndexed && IsGrayscalePalette(input.Palette))
        //    {
        //        Console.WriteLine($"ConvertTo8BitGray: 输入已是合法8位灰度图，直接返回副本");
        //        return new Bitmap(input);
        //    }

        //    int width = input.Width;
        //    int height = input.Height;

        //    // 创建临时32位ARGB图像（非索引格式，支持所有像素操作）
        //    using (Bitmap tempBitmap = new Bitmap(width, height, PixelFormat.Format32bppArgb))
        //    {
        //        // 手动复制像素数据，不使用Graphics对象
        //        BitmapData inputData = input.LockBits(
        //            new Rectangle(0, 0, width, height),
        //            ImageLockMode.ReadOnly,
        //            input.PixelFormat
        //        );

        //        BitmapData tempData = tempBitmap.LockBits(
        //            new Rectangle(0, 0, width, height),
        //            ImageLockMode.WriteOnly,
        //            PixelFormat.Format32bppArgb
        //        );

        //        try
        //        {
        //            // 手动复制像素数据
        //            unsafe
        //            {
        //                byte* inputPtr = (byte*)inputData.Scan0;
        //                byte* tempPtr = (byte*)tempData.Scan0;
        //                int inputStride = inputData.Stride;
        //                int tempStride = tempData.Stride;

        //                for (int y = 0; y < height; y++)
        //                {
        //                    for (int x = 0; x < width; x++)
        //                    {
        //                        // 根据输入图像格式获取像素值
        //                        byte r, g, b;

        //                        switch (input.PixelFormat)
        //                        {
        //                            case PixelFormat.Format24bppRgb:
        //                                b = inputPtr[y * inputStride + x * 3];
        //                                g = inputPtr[y * inputStride + x * 3 + 1];
        //                                r = inputPtr[y * inputStride + x * 3 + 2];
        //                                break;
        //                            case PixelFormat.Format32bppArgb:
        //                            case PixelFormat.Format32bppRgb:
        //                                b = inputPtr[y * inputStride + x * 4];
        //                                g = inputPtr[y * inputStride + x * 4 + 1];
        //                                r = inputPtr[y * inputStride + x * 4 + 2];
        //                                break;
        //                            case PixelFormat.Format8bppIndexed:
        //                                // 对于索引格式，直接获取索引值（假设为灰度）
        //                                byte index = inputPtr[y * inputStride + x];
        //                                r = g = b = index;
        //                                break;
        //                            default:
        //                                throw new NotSupportedException($"不支持的像素格式: {input.PixelFormat}");
        //                        }

        //                        // 设置临时图像的像素值（32位ARGB）
        //                        tempPtr[y * tempStride + x * 4] = b;       // B
        //                        tempPtr[y * tempStride + x * 4 + 1] = g;   // G
        //                        tempPtr[y * tempStride + x * 4 + 2] = r;   // R
        //                        tempPtr[y * tempStride + x * 4 + 3] = 255; // A（不透明）
        //                    }
        //                }
        //            }
        //        }
        //        finally
        //        {
        //            input.UnlockBits(inputData);
        //            tempBitmap.UnlockBits(tempData);
        //        }

        //        // 创建8位索引灰度图
        //        Bitmap grayBitmap = new Bitmap(width, height, PixelFormat.Format8bppIndexed);

        //        // 设置灰度调色板
        //        ColorPalette palette = grayBitmap.Palette;
        //        for (int i = 0; i < 256; i++)
        //        {
        //            palette.Entries[i] = Color.FromArgb(i, i, i);
        //        }
        //        grayBitmap.Palette = palette;

        //        // 再次锁定图像进行灰度转换
        //        tempData = tempBitmap.LockBits(
        //            new Rectangle(0, 0, width, height),
        //            ImageLockMode.ReadOnly,
        //            PixelFormat.Format32bppArgb
        //        );

        //        BitmapData grayData = grayBitmap.LockBits(
        //            new Rectangle(0, 0, width, height),
        //            ImageLockMode.WriteOnly,
        //            PixelFormat.Format8bppIndexed
        //        );

        //        try
        //        {
        //            // 计算灰度值并写入8位图像
        //            unsafe
        //            {
        //                byte* tempPtr = (byte*)tempData.Scan0;
        //                byte* grayPtr = (byte*)grayData.Scan0;
        //                int tempStride = tempData.Stride;
        //                int grayStride = grayData.Stride;

        //                for (int y = 0; y < height; y++)
        //                {
        //                    for (int x = 0; x < width; x++)
        //                    {
        //                        byte b = tempPtr[y * tempStride + x * 4];
        //                        byte g = tempPtr[y * tempStride + x * 4 + 1];
        //                        byte r = tempPtr[y * tempStride + x * 4 + 2];

        //                        // 计算灰度值（使用标准公式）
        //                        byte gray = (byte)(0.299 * r + 0.587 * g + 0.114 * b);

        //                        // 设置8位灰度图像的像素值
        //                        grayPtr[y * grayStride + x] = gray;
        //                    }
        //                }
        //            }
        //        }
        //        finally
        //        {
        //            tempBitmap.UnlockBits(tempData);
        //            grayBitmap.UnlockBits(grayData);
        //        }

        //        Console.WriteLine($"ConvertTo8BitGray: 成功转换为8位灰度图");
        //        return grayBitmap;
        //    }
        //}

        // 检查调色板是否为灰度

        private static Bitmap ConvertTo8BitGray(Bitmap input)
        {
            int width = input.Width;
            int height = input.Height;

            // 创建32位ARGB临时图像（非索引格式，支持内存对齐）
            using (Bitmap tempBitmap = new Bitmap(width, height, PixelFormat.Format32bppArgb))
            {
                // 绘制输入图像到临时图像（安全使用Graphics，因临时图像是非索引格式）
                using (Graphics g = Graphics.FromImage(tempBitmap))
                {
                    g.DrawImage(input, new Rectangle(0, 0, width, height));
                }

                // 创建8位索引灰度图（目标图像）
                Bitmap grayBitmap = new Bitmap(width, height, PixelFormat.Format8bppIndexed);
                // 设置灰度调色板
                ColorPalette palette = grayBitmap.Palette;
                for (int i = 0; i < 256; i++)
                {
                    palette.Entries[i] = Color.FromArgb(i, i, i);
                }
                grayBitmap.Palette = palette;

                // 锁定临时图像和目标图像的内存
                BitmapData tempData = tempBitmap.LockBits(
                    new Rectangle(0, 0, width, height),
                    ImageLockMode.ReadOnly,
                    tempBitmap.PixelFormat
                );
                BitmapData grayData = grayBitmap.LockBits(
                    new Rectangle(0, 0, width, height),
                    ImageLockMode.WriteOnly,
                    grayBitmap.PixelFormat
                );

                try
                {
                    unsafe
                    {
                        byte* tempPtr = (byte*)tempData.Scan0;  // 临时图像数据指针
                        byte* grayPtr = (byte*)grayData.Scan0;  // 目标图像数据指针
                        int tempStride = tempData.Stride;       // 临时图像每行字节数（可能大于width*4）
                        int grayStride = grayData.Stride;       // 目标图像每行字节数（可能大于width）

                        // 逐行处理像素
                        for (int y = 0; y < height; y++)
                        {
                            // 计算当前行的起始指针
                            byte* tempRow = tempPtr + y * tempStride;  // 临时图像当前行
                            byte* grayRow = grayPtr + y * grayStride;  // 目标图像当前行

                            // 只复制有效宽度的像素（忽略Stride中的填充字节）
                            for (int x = 0; x < width; x++)
                            {
                                // 从32位ARGB取B、G、R值（注意：Bitmap中是BGR顺序）
                                byte b = tempRow[x * 4];
                                byte g = tempRow[x * 4 + 1];
                                byte r = tempRow[x * 4 + 2];

                                // 计算灰度值
                                byte gray = (byte)(0.299 * r + 0.587 * g + 0.114 * b);

                                // 写入目标图像的当前像素（只写有效宽度内的像素）
                                grayRow[x] = gray;
                            }
                        }
                    }
                }
                finally
                {
                    // 解锁内存
                    tempBitmap.UnlockBits(tempData);
                    grayBitmap.UnlockBits(grayData);
                }

                return grayBitmap;
            }
        }

        // 检查调色板是否为灰度
        private static bool IsGrayscalePalette(ColorPalette palette)
        {
            for (int i = 0; i < palette.Entries.Length; i++)
            {
                Color c = palette.Entries[i];
                if (c.R != c.G || c.R != c.B || c.A != 255)
                {
                    return false;
                }
            }
            return true;
        }


        //private static bool IsGrayscalePalette(ColorPalette palette)
        //{
        //    for (int i = 0; i < palette.Entries.Length; i++)
        //    {
        //        Color c = palette.Entries[i];
        //        if (c.R != c.G || c.R != c.B || c.A != 255)
        //        {
        //            return false;
        //        }
        //    }
        //    return true;
        //}

        [DllImport("gdi32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool DeleteObject(IntPtr hObject);

        public void FreeBitmap(IntPtr hbitmap)
        {
            if (hbitmap != IntPtr.Zero)
            {
                DeleteObject(hbitmap);
            }
        }
    }
}